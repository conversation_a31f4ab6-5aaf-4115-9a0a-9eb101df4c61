#!/usr/bin/env python3
"""
Simple test to verify that the knowledge base database is created in the .codemate folder.
This test checks the database file location without creating actual knowledge bases.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch

def test_database_location():
    """Test that the database file is created in the .codemate folder."""
    
    # Create a temporary directory to simulate the home directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_home = Path(temp_dir)
        expected_codemate_path = temp_home / ".codemate"
        expected_db_path = expected_codemate_path / "knowledge_bases_db_codemate.json"
        
        # Mock PathSelector.get_base_path to return our temp directory
        with patch('BASE.utils.path_selector.PathSelector.get_base_path', return_value=expected_codemate_path):
            # Import the service after patching
            from BASE.services.knowledge_bases import get_knowledge_bases_collection
            
            # Call the function to initialize the database
            collection = get_knowledge_bases_collection()
            
            # Check that the .codemate directory was created
            assert expected_codemate_path.exists(), f".codemate directory not created at {expected_codemate_path}"
            
            # Check that the database file was created in the correct location
            assert expected_db_path.exists(), f"Database file not created at {expected_db_path}"
            
            print(f"✓ .codemate directory created: {expected_codemate_path}")
            print(f"✓ Database file created: {expected_db_path}")
            print("✓ Test passed: Database is correctly saved in .codemate folder")

if __name__ == "__main__":
    test_database_location()
